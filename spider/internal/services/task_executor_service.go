package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"shovel/spider/internal/entity"
	"shovel/spider/internal/models"
	"strings"
	"sync"
	"time"

	"github.com/dapr/go-sdk/client"
	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
)

// TaskExecutorService 任务执行服务
type TaskExecutorService struct {
	daprClient       client.Client
	taskManagerAppID string
	httpClient       *http.Client
	pubsubName       string
	topicName        string
	// TaskWorker管理
	taskWorkers map[string]*entity.TaskWorker // TaskID -> TaskWorker映射
	workerMutex sync.RWMutex                  // 保护taskWorkers的读写锁
}

// NewTaskExecutorService 创建任务执行服务
func NewTaskExecutorService(taskManagerAppID, pubsubName, topicName string) (*TaskExecutorService, error) {
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	return &TaskExecutorService{
		daprClient:       daprClient,
		taskManagerAppID: taskManagerAppID,
		pubsubName:       pubsubName,
		topicName:        topicName,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		taskWorkers: make(map[string]*entity.TaskWorker),
	}, nil
}

// Start 启动任务执行服务
func (d *TaskExecutorService) Start(ctx context.Context) error {
	log.Println("启动任务执行服务...")

	// 从环境变量获取端口号
	port := os.Getenv("SERVER_PORT")
	if port == "" {
		port = "8082" // 默认端口
	}

	// 创建Dapr HTTP服务
	daprSrv := daprd.NewService(":" + port)

	// 订阅Pubsub事件
	subscription := &common.Subscription{
		PubsubName: d.pubsubName,
		Topic:      d.topicName,
		Route:      "/download-url",
	}

	if err := daprSrv.AddTopicEventHandler(subscription, d.handleURLDownload); err != nil {
		return fmt.Errorf("添加事件处理器失败: %w", err)
	}

	log.Println("任务执行服务已启动，等待Pubsub消息...")

	// 启动服务
	return daprSrv.Start()
}

// handleURLDownload 处理URL下载事件
func (d *TaskExecutorService) handleURLDownload(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
	var urlItem models.URLQueueItem

	// 处理可能的双重JSON编码情况
	var rawJSON string
	if err := json.Unmarshal(e.RawData, &rawJSON); err == nil {
		// 如果成功解析为字符串，说明是双重编码的JSON
		if err := json.Unmarshal([]byte(rawJSON), &urlItem); err != nil {
			log.Printf("解析URL数据失败: %v", err)
			return false, err
		}
	} else {
		// 尝试直接解析
		if err := json.Unmarshal(e.RawData, &urlItem); err != nil {
			log.Printf("解析URL数据失败: %v", err)
			return false, err
		}
	}

	// 验证必要字段
	if urlItem.ID <= 0 {
		log.Printf("无效的URL ID: %d", urlItem.ID)
		return false, fmt.Errorf("无效的URL ID: %d", urlItem.ID)
	}

	if urlItem.URL == "" {
		log.Printf("URL为空: ID=%d", urlItem.ID)
		return false, fmt.Errorf("URL为空: ID=%d", urlItem.ID)
	}

	if urlItem.TaskID == "" {
		log.Printf("URL所属任务ID为空: URL ID=%d", urlItem.ID)
		return false, fmt.Errorf("任务ID不能为空")
	}

	log.Printf("开始处理URL: ID=%d, URL=%s, TaskID=%s", urlItem.ID, urlItem.URL, urlItem.TaskID)

	// 获取任务信息
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	task, err := d.getTaskInfo(timeoutCtx, urlItem.TaskID)
	if err != nil {
		log.Printf("获取URL的任务信息失败: %v", err)
		return false, err
	}
	log.Printf("获取URL的任务信息成功: %v", task)

	// 获取或创建对应TaskID的TaskWorker
	taskWorker, err := d.getOrCreateTaskWorker(urlItem.TaskID, task.MaxConcurrency)
	if err != nil {
		log.Printf("创建TaskWorker失败: %v", err)
		return false, err
	}

	// 爬取func
	crawlTask := func(taskCtx context.Context) {
		// 更新状态为processing
		updateStatusErr := d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusProcessing, "")
		if updateStatusErr != nil {
			log.Printf("更新URL状态为processing失败: %v, id=%d", updateStatusErr, urlItem.ID)
			return
		}

		// 调用Spider服务的crawl接口
		// 超时在URLItem配置的timeout+10秒
		timeout := time.Duration(task.Timeout+10) * time.Second
		timeoutCtx, cancel := context.WithTimeout(taskCtx, timeout)
		defer cancel()
		crawlErr := d.callSpiderCrawl(timeoutCtx, urlItem, task)

		if crawlErr == nil {
			// 更新状态为completed
			for i := 0; i < 3; i++ {
				updateStatusErr = d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusCompleted, "")
				if updateStatusErr == nil {
					break
				}
				log.Printf("更新URL状态为completed失败(尝试 %d/3): %v, id=%d", i+1, updateStatusErr, urlItem.ID)
				time.Sleep(time.Second * time.Duration(i+1)) // 指数退避
			}

			if updateStatusErr != nil {
				log.Printf("更新URL状态为completed最终失败: %v id=%d", updateStatusErr, urlItem.ID)
			} else {
				log.Printf("URL爬取完成: id=%d", urlItem.ID)
			}
		} else {
			log.Printf("URL爬取失败: %v id=%d url=%s", crawlErr, urlItem.ID, urlItem.URL)
			// 更新状态为failed
			for i := 0; i < 3; i++ {
				updateStatusErr = d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusFailed, crawlErr.Error())
				if updateStatusErr == nil {
					break
				}
				log.Printf("更新URL状态为failed失败(尝试 %d/3): %v, id=%d", i+1, updateStatusErr, urlItem.ID)
				time.Sleep(time.Second * time.Duration(i+1)) // 指数退避
			}

			if updateStatusErr != nil {
				log.Printf("更新URL状态为failed最终失败: %v id=%d", updateStatusErr, urlItem.ID)
			}
		}
	} // crawlTask := func(taskCtx context.Context) end

	// 提交爬取func到TaskWorker
	if !taskWorker.AddTask(crawlTask) {
		log.Printf("提交爬取func到TaskWorker失败: url_id=%d", urlItem.ID)
		return false, fmt.Errorf("提交爬取func到TaskWorker失败")
	}

	// 返回值解释: 是否重试, 错误信息
	return false, nil
}

// getOrCreateTaskWorker 获取或创建指定TaskID的TaskWorker
func (d *TaskExecutorService) getOrCreateTaskWorker(taskID string, maxConcurrency int) (*entity.TaskWorker, error) {
	// 如果taskID为空，直接返回错误
	if taskID == "" {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	// 先尝试从map中获取现有TaskWorker
	d.workerMutex.RLock()
	worker, exists := d.taskWorkers[taskID]
	d.workerMutex.RUnlock()

	if exists {
		return worker, nil
	}

	// 不存在，则创建新的TaskWorker
	d.workerMutex.Lock()
	defer d.workerMutex.Unlock()

	// 双重检查，防止在获取写锁期间其他goroutine已创建
	if worker, exists = d.taskWorkers[taskID]; exists {
		return worker, nil
	}

	// 检查并发数是否有效
	if maxConcurrency <= 0 {
		return nil, fmt.Errorf("无效的并发数配置: %d", maxConcurrency)
	}

	// 创建新的TaskWorker，缓冲区大小设为并发数的10倍
	worker = entity.NewTaskWorker(maxConcurrency, maxConcurrency*10)
	worker.Start()

	// 存储到map中
	d.taskWorkers[taskID] = worker
	log.Printf("成功新创建TaskWorker: TaskID=%s, MaxConcurrency=%d", taskID, maxConcurrency)

	return worker, nil
}

// getTaskInfo 通过Dapr调用获取任务信息
func (d *TaskExecutorService) getTaskInfo(ctx context.Context, taskID string) (*models.CrawlerTask, error) {
	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/tasks/%s", taskID)
	// 使用 HTTP 客户端代理模式替代 InvokeMethod
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", d.taskManagerAppID, methodPath)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return nil, fmt.Errorf("调用服务超时: %w", err)
		} else if ctx.Err() == context.Canceled {
			return nil, fmt.Errorf("调用服务请求被取消: %w", err)
		}
		return nil, fmt.Errorf("调用服务失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取服务响应失败: %w", err)
	}

	// 解析响应结构体
	var response struct {
		Success bool               `json:"success"`
		Data    models.CrawlerTask `json:"data"`
		Error   string             `json:"error"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析服务响应失败: %w 响应体=%s", err, string(respBody))
	}

	// 检查响应是否成功
	if !response.Success {
		return nil, fmt.Errorf("获取任务信息失败: %s", response.Error)
	}

	return &response.Data, nil
}

// updateURLStatus 更新URL状态
func (d *TaskExecutorService) updateURLStatus(ctx context.Context, urlID int64, status models.URLStatus, errorMessage string) error {
	// 验证状态值是否有效
	validStatuses := map[models.URLStatus]bool{
		models.URLStatusPending:    true,
		models.URLStatusScheduled:  true,
		models.URLStatusProcessing: true,
		models.URLStatusCompleted:  true,
		models.URLStatusFailed:     true,
	}

	if !validStatuses[status] {
		return fmt.Errorf("无效的状态值: %s", status)
	}

	// 构建请求体
	payload := map[string]interface{}{
		"status": string(status), // 确保使用字符串格式
	}
	if errorMessage != "" {
		payload["error_message"] = errorMessage
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 使用 HTTP 客户端代理模式替代 InvokeMethodWithContent
	// 设置超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/urls/%d/status", urlID)
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", d.taskManagerAppID, methodPath)

	req, err := http.NewRequestWithContext(timeoutCtx, "PUT", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			return fmt.Errorf("调用服务超时: %w", err)
		} else if timeoutCtx.Err() == context.Canceled {
			return fmt.Errorf("调用服务请求被取消: %w", err)
		} else {
			return fmt.Errorf("调用服务失败: %w 请求体=%s", err, string(data))
		}
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取服务响应失败: %w", err)
	}

	// 尝试解析错误响应
	if resp.StatusCode >= 400 {
		var errResp struct {
			Success bool   `json:"success"`
			Message string `json:"message"`
			Error   string `json:"error"`
		}

		if len(respBody) > 0 {
			if jsonErr := json.Unmarshal(respBody, &errResp); jsonErr == nil {
				return fmt.Errorf("服务返回错误: 状态码=%d 消息=%s 错误=%s", resp.StatusCode, errResp.Message, errResp.Error)
			} else {
				return fmt.Errorf("解析服务错误响应失败: %w 状态码=%d 原始响应=%s", jsonErr, resp.StatusCode, string(respBody))
			}
		} else {
			return fmt.Errorf("服务返回错误: 状态码=%d", resp.StatusCode)
		}
	}

	return nil
}

// callSpiderCrawl 调用Spider服务的crawl接口
func (d *TaskExecutorService) callSpiderCrawl(ctx context.Context, urlItem models.URLQueueItem, task *models.CrawlerTask) error {
	// 序列化URL项
	data, err := json.Marshal(urlItem)
	if err != nil {
		return fmt.Errorf("序列化URL数据失败: %w", err)
	}

	// 使用任务中的spiderName作为Spider服务的应用ID
	spiderAppID := task.SpiderName

	// 使用 HTTP 客户端代理模式替代 InvokeMethodWithContent
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", spiderAppID, "crawl")

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return fmt.Errorf("调用服务超时: %w", err)
		} else if ctx.Err() == context.Canceled {
			return fmt.Errorf("调用服务请求被取消: %w", err)
		}
		return fmt.Errorf("调用服务失败: %w 请求体=%s", err, string(data))
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取服务响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		return fmt.Errorf("服务返回错误: 状态码=%d 响应体=%s", resp.StatusCode, strings.TrimSpace(string(respBody)))
	}

	// 解析响应
	var response struct {
		Success bool                  `json:"success"`
		Message string                `json:"message"`
		URLID   int64                 `json:"url_id,omitempty"`
		URLs    []models.URLQueueItem `json:"extracted_urls,omitempty"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return fmt.Errorf("解析服务响应失败: %w 响应体=%s", err, string(respBody))
	}

	if !response.Success {
		return fmt.Errorf("服务返回错误: 消息=%s", response.Message)
	}

	return nil
}

// Close 关闭服务
func (d *TaskExecutorService) Close() error {
	// 停止所有TaskWorker
	d.workerMutex.Lock()
	for taskID, worker := range d.taskWorkers {
		log.Printf("正在停止TaskWorker: TaskID=%s", taskID)
		worker.Stop()
	}
	// 清空map
	d.taskWorkers = make(map[string]*entity.TaskWorker)
	d.workerMutex.Unlock()

	// 关闭Dapr客户端
	if d.daprClient != nil {
		d.daprClient.Close()
	}

	log.Println("任务执行服务已关闭")
	return nil
}
